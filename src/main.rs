//! Command-line interface for get_machineid
//!
//! This binary provides a command-line interface to the get_machineid library,
//! allowing users to retrieve machine IDs from the command line.

use clap::{Arg, ArgAction, Command};
use get_machineid::{id, protected_id};
use std::process;

const VERSION: &str = env!("CARGO_PKG_VERSION");
const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

fn main() {
    let matches = Command::new("get_machineid")
        .version(VERSION)
        .about(DESCRIPTION)
        .long_about(format!(
            "{}\n\n\
            This tool provides cross-platform access to unique machine identifiers \
            without requiring administrator privileges. For security reasons, consider \
            using the --protected option instead of the raw machine ID.",
            DESCRIPTION
        ))
        .arg(
            Arg::new("raw")
                .short('r')
                .long("raw")
                .help("Display the raw machine ID (default behavior)")
                .action(ArgAction::SetTrue)
                .conflicts_with("protected")
        )
        .arg(
            Arg::new("protected")
                .short('p')
                .long("protected")
                .help("Display protected machine ID hashed with the given application ID")
                .value_name("APP_ID")
                .conflicts_with("raw")
        )
        .arg(
            Arg::new("appid")
                .long("appid")
                .help("Protect machine ID by hashing it with an app ID (legacy compatibility)")
                .value_name("APP_ID")
                .conflicts_with_all(["raw", "protected"])
                .hide(true) // Hidden for backward compatibility
        )
        .after_help(
            "EXAMPLES:\n  \
            get_machineid                           # Display raw machine ID\n  \
            get_machineid --raw                     # Display raw machine ID (explicit)\n  \
            get_machineid --protected MyApp         # Display protected ID for 'MyApp'\n  \
            get_machineid --appid MyApp             # Legacy syntax (same as --protected)\n\n\
            SECURITY NOTE:\n  \
            The raw machine ID should be considered confidential. Use --protected \n  \
            for application-specific identification in production environments."
        )
        .get_matches();

    // Determine which operation to perform
    let result = if let Some(app_id) = matches.get_one::<String>("protected") {
        // Protected ID with modern syntax
        get_protected_id(app_id)
    } else if let Some(app_id) = matches.get_one::<String>("appid") {
        // Protected ID with legacy syntax for backward compatibility
        get_protected_id(app_id)
    } else {
        // Raw ID (default behavior or explicit --raw)
        get_raw_id()
    };

    // Handle the result
    match result {
        Ok(machine_id) => {
            println!("{}", machine_id);
        }
        Err(e) => {
            eprintln!("Error: Failed to read machine ID: {}", e);
            process::exit(1);
        }
    }
}

/// Get the raw machine ID
fn get_raw_id() -> Result<String, Box<dyn std::error::Error>> {
    let machine_id = id()?;
    Ok(machine_id)
}

/// Get the protected machine ID for the given application ID
fn get_protected_id(app_id: &str) -> Result<String, Box<dyn std::error::Error>> {
    if app_id.is_empty() {
        return Err("Application ID cannot be empty".into());
    }
    
    let protected_machine_id = protected_id(app_id)?;
    Ok(protected_machine_id)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_raw_id() {
        let result = get_raw_id();
        assert!(result.is_ok(), "Failed to get raw machine ID");
        let id = result.unwrap();
        assert!(!id.is_empty(), "Raw machine ID should not be empty");
    }

    #[test]
    fn test_get_protected_id() {
        let app_id = "test_app";
        let result = get_protected_id(app_id);
        assert!(result.is_ok(), "Failed to get protected machine ID");
        let id = result.unwrap();
        assert!(!id.is_empty(), "Protected machine ID should not be empty");
        assert_eq!(id.len(), 64, "Protected ID should be 64 characters (SHA256 hex)");
    }

    #[test]
    fn test_get_protected_id_empty_app_id() {
        let result = get_protected_id("");
        assert!(result.is_err(), "Empty app ID should return an error");
    }

    #[test]
    fn test_protected_id_consistency() {
        let app_id = "consistent_test";
        let id1 = get_protected_id(app_id).unwrap();
        let id2 = get_protected_id(app_id).unwrap();
        assert_eq!(id1, id2, "Protected IDs should be consistent for the same app ID");
    }

    #[test]
    fn test_different_app_ids_different_protected_ids() {
        let id1 = get_protected_id("app1").unwrap();
        let id2 = get_protected_id("app2").unwrap();
        assert_ne!(id1, id2, "Different app IDs should produce different protected IDs");
    }
}
