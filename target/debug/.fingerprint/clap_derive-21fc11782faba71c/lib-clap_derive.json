{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 10242616682602009136, "path": 3833266431416864318, "deps": [[3060637413840920116, "proc_macro2", false, 14772536725519780437], [4974441333307933176, "syn", false, 4508062084159226607], [13077543566650298139, "heck", false, 9452326547541577744], [17990358020177143287, "quote", false, 7116720639529496799]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_derive-21fc11782faba71c/dep-lib-clap_derive", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}