//! Platform-specific implementations for reading machine IDs

use crate::{trim, MachineIdError};

#[cfg(any(target_os = "linux", target_os = "freebsd", target_os = "netbsd", target_os = "openbsd", target_os = "dragonfly"))]
use std::fs;

#[cfg(target_os = "linux")]
pub fn machine_id() -> Result<String, MachineIdError> {
    const DBUS_PATH: &str = "/var/lib/dbus/machine-id";
    const DBUS_PATH_ETC: &str = "/etc/machine-id";

    // Try the primary path first
    match fs::read_to_string(DBUS_PATH) {
        Ok(content) => return Ok(trim(&content)),
        Err(_) => {
            // Try fallback path
            match fs::read_to_string(DBUS_PATH_ETC) {
                Ok(content) => return Ok(trim(&content)),
                Err(e) => return Err(MachineIdError::from(e)),
            }
        }
    }
}

#[cfg(target_os = "macos")]
pub fn machine_id() -> Result<String, MachineIdError> {
    use std::process::Command;

    let output = Command::new("ioreg")
        .args(&["-rd1", "-c", "IOPlatformExpertDevice"])
        .output()
        .map_err(|e| MachineIdError::from(format!("Failed to execute ioreg: {}", e)))?;

    if !output.status.success() {
        return Err(MachineIdError::from("ioreg command failed"));
    }

    let output_str = String::from_utf8(output.stdout)
        .map_err(|e| MachineIdError::from(format!("Invalid UTF-8 output: {}", e)))?;

    extract_io_platform_uuid(&output_str)
}

#[cfg(target_os = "macos")]
fn extract_io_platform_uuid(lines: &str) -> Result<String, MachineIdError> {
    for line in lines.lines() {
        if line.contains("IOPlatformUUID") {
            let parts: Vec<&str> = line.splitn(2, " = \"").collect();
            if parts.len() == 2 {
                let uuid_part = parts[1].trim_end_matches('"');
                return Ok(trim(uuid_part));
            }
        }
    }
    Err(MachineIdError::from(format!(
        "Failed to extract 'IOPlatformUUID' value from ioreg output.\n{}",
        lines
    )))
}

#[cfg(target_os = "windows")]
pub fn machine_id() -> Result<String, MachineIdError> {
    use winreg::enums::*;
    use winreg::RegKey;

    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let key = hklm
        .open_subkey_with_flags(r"SOFTWARE\Microsoft\Cryptography", KEY_QUERY_VALUE | KEY_WOW64_64KEY)
        .map_err(|e| MachineIdError::from(format!("Failed to open registry key: {}", e)))?;

    let machine_guid: String = key
        .get_value("MachineGuid")
        .map_err(|e| MachineIdError::from(format!("Failed to read MachineGuid: {}", e)))?;

    Ok(machine_guid)
}

#[cfg(any(
    target_os = "freebsd",
    target_os = "netbsd",
    target_os = "openbsd",
    target_os = "dragonfly"
))]
pub fn machine_id() -> Result<String, MachineIdError> {
    const HOSTID_PATH: &str = "/etc/hostid";

    // Try reading from /etc/hostid first
    match read_hostid() {
        Ok(id) => Ok(id),
        Err(_) => {
            // Try fallback to kenv
            read_kenv()
        }
    }
}

#[cfg(any(
    target_os = "freebsd",
    target_os = "netbsd",
    target_os = "openbsd",
    target_os = "dragonfly"
))]
fn read_hostid() -> Result<String, MachineIdError> {
    const HOSTID_PATH: &str = "/etc/hostid";
    let content = fs::read_to_string(HOSTID_PATH)?;
    Ok(trim(&content))
}

#[cfg(any(
    target_os = "freebsd",
    target_os = "netbsd",
    target_os = "openbsd",
    target_os = "dragonfly"
))]
fn read_kenv() -> Result<String, MachineIdError> {
    use std::process::Command;

    let output = Command::new("kenv")
        .args(&["-q", "smbios.system.uuid"])
        .output()
        .map_err(|e| MachineIdError::from(format!("Failed to execute kenv: {}", e)))?;

    if !output.status.success() {
        return Err(MachineIdError::from("kenv command failed"));
    }

    let output_str = String::from_utf8(output.stdout)
        .map_err(|e| MachineIdError::from(format!("Invalid UTF-8 output: {}", e)))?;

    Ok(trim(&output_str))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_machine_id() {
        let result = machine_id();
        assert!(result.is_ok(), "Failed to get machine ID: {:?}", result);
        let id = result.unwrap();
        assert!(!id.is_empty(), "Got empty machine ID");
        println!("Machine ID: {}", id);
    }

    #[cfg(target_os = "macos")]
    #[test]
    fn test_extract_io_platform_uuid() {
        let sample_output = r#"+-o Root  <class IORegistryEntry, id 0x100000100, retain 8>
  {
    "IOKitBuildVersion" = "xnu-8020.121.3~4/RELEASE_ARM64_T6000"
    "IOPlatformUUID" = "12345678-1234-1234-1234-123456789ABC"
    "IORegistryEntryName" = "Root"
  }"#;

        let result = extract_io_platform_uuid(sample_output);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "12345678-1234-1234-1234-123456789ABC");
    }

    #[cfg(target_os = "macos")]
    #[test]
    fn test_extract_io_platform_uuid_not_found() {
        let sample_output = r#"+-o Root  <class IORegistryEntry, id 0x100000100, retain 8>
  {
    "IOKitBuildVersion" = "xnu-8020.121.3~4/RELEASE_ARM64_T6000"
    "IORegistryEntryName" = "Root"
  }"#;

        let result = extract_io_platform_uuid(sample_output);
        assert!(result.is_err());
    }
}
