{"rustc": 12610991425282158916, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 4508661205163583630, "deps": [[5820056977320921005, "anstream", false, 4460149673293405432], [9394696648929125047, "anstyle", false, 16326833396047164477], [11166530783118767604, "strsim", false, 11269356614193932537], [11649982696571033535, "clap_lex", false, 15086451922259426249]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-f212b9c6ebcb67bb/dep-lib-clap_builder", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}