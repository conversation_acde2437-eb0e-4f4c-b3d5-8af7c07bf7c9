{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"reset\", \"std\"]", "target": 12991177224612424488, "profile": 5347358027863023418, "path": 16303189959414909244, "deps": [[17475753849556516473, "digest", false, 15231276259199091230]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hmac-5f99da8cd5ebdda2/dep-lib-hmac", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}