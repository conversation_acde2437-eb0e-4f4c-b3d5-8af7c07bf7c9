//! Simple benchmark example for get_machineid crate
//!
//! This example demonstrates the performance characteristics of the library.

use get_machineid::{id, protected_id};
use std::time::Instant;

fn main() {
    println!("=== get_machineid Performance Benchmark ===\n");

    // Benchmark raw ID retrieval
    let start = Instant::now();
    let iterations = 1000;
    
    for _ in 0..iterations {
        let _ = id().expect("Failed to get machine ID");
    }
    
    let duration = start.elapsed();
    let avg_time = duration / iterations;
    
    println!("Raw ID retrieval:");
    println!("  {} iterations in {:?}", iterations, duration);
    println!("  Average time per call: {:?}", avg_time);
    println!("  Calls per second: {:.0}", 1.0 / avg_time.as_secs_f64());
    
    // Benchmark protected ID generation
    let start = Instant::now();
    let app_id = "benchmark.test.app";
    
    for _ in 0..iterations {
        let _ = protected_id(app_id).expect("Failed to get protected ID");
    }
    
    let duration = start.elapsed();
    let avg_time = duration / iterations;
    
    println!("\nProtected ID generation:");
    println!("  {} iterations in {:?}", iterations, duration);
    println!("  Average time per call: {:?}", avg_time);
    println!("  Calls per second: {:.0}", 1.0 / avg_time.as_secs_f64());
    
    // Show sample output
    println!("\n=== Sample Output ===");
    let machine_id = id().expect("Failed to get machine ID");
    let protected = protected_id("sample.app").expect("Failed to get protected ID");
    
    println!("Machine ID: {}", machine_id);
    println!("Protected ID: {}", protected);
}
