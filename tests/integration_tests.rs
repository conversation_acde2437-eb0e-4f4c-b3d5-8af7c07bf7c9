//! Integration tests for get_machineid crate

use get_machineid::{id, protected_id};

#[test]
fn test_id_returns_non_empty_string() {
    let result = id();
    assert!(result.is_ok(), "Failed to get machine ID: {:?}", result);
    
    let machine_id = result.unwrap();
    assert!(!machine_id.is_empty(), "Machine ID should not be empty");
    assert!(!machine_id.trim().is_empty(), "Machine ID should not be just whitespace");
}

#[test]
fn test_id_is_consistent() {
    let id1 = id().expect("Failed to get first machine ID");
    let id2 = id().expect("Failed to get second machine ID");
    
    assert_eq!(id1, id2, "Machine ID should be consistent across calls");
}

#[test]
fn test_protected_id_returns_non_empty_string() {
    let app_id = "test.app.id";
    let result = protected_id(app_id);
    assert!(result.is_ok(), "Failed to get protected ID: {:?}", result);
    
    let protected = result.unwrap();
    assert!(!protected.is_empty(), "Protected ID should not be empty");
    assert!(!protected.trim().is_empty(), "Protected ID should not be just whitespace");
}

#[test]
fn test_protected_id_is_different_from_raw_id() {
    let raw_id = id().expect("Failed to get raw machine ID");
    let protected = protected_id("test.app").expect("Failed to get protected ID");
    
    assert_ne!(raw_id, protected, "Protected ID should be different from raw ID");
}

#[test]
fn test_protected_id_is_consistent() {
    let app_id = "consistent.test.app";
    let protected1 = protected_id(app_id).expect("Failed to get first protected ID");
    let protected2 = protected_id(app_id).expect("Failed to get second protected ID");
    
    assert_eq!(protected1, protected2, "Protected ID should be consistent for same app ID");
}

#[test]
fn test_different_app_ids_produce_different_protected_ids() {
    let app1 = "app.one";
    let app2 = "app.two";
    
    let protected1 = protected_id(app1).expect("Failed to get protected ID for app1");
    let protected2 = protected_id(app2).expect("Failed to get protected ID for app2");
    
    assert_ne!(protected1, protected2, "Different app IDs should produce different protected IDs");
}

#[test]
fn test_protected_id_is_hex_encoded() {
    let protected = protected_id("hex.test").expect("Failed to get protected ID");
    
    // Should be valid hex (64 characters for SHA256)
    assert_eq!(protected.len(), 64, "Protected ID should be 64 characters (SHA256 hex)");
    
    // Should only contain hex characters
    for c in protected.chars() {
        assert!(c.is_ascii_hexdigit(), "Protected ID should only contain hex characters");
    }
}

#[test]
fn test_empty_app_id() {
    let result = protected_id("");
    assert!(result.is_ok(), "Empty app ID should be valid");
    
    let protected = result.unwrap();
    assert!(!protected.is_empty(), "Protected ID should not be empty even with empty app ID");
}

#[test]
fn test_special_characters_in_app_id() {
    let special_app_ids = [
        "app with spaces",
        "app-with-dashes",
        "app_with_underscores",
        "app.with.dots",
        "app/with/slashes",
        "app@with#special$chars%",
        "🦀 rust app 🦀",
    ];
    
    for app_id in &special_app_ids {
        let result = protected_id(app_id);
        assert!(result.is_ok(), "Failed to get protected ID for app_id: {}", app_id);
        
        let protected = result.unwrap();
        assert!(!protected.is_empty(), "Protected ID should not be empty for app_id: {}", app_id);
        assert_eq!(protected.len(), 64, "Protected ID should be 64 characters for app_id: {}", app_id);
    }
}

#[test]
fn test_long_app_id() {
    let long_app_id = "a".repeat(1000);
    let result = protected_id(&long_app_id);
    assert!(result.is_ok(), "Long app ID should be valid");
    
    let protected = result.unwrap();
    assert_eq!(protected.len(), 64, "Protected ID should still be 64 characters for long app ID");
}

#[cfg(target_os = "linux")]
#[test]
fn test_linux_machine_id_format() {
    let machine_id = id().expect("Failed to get machine ID on Linux");
    
    // Linux machine IDs are typically 32 hex characters
    assert!(machine_id.len() >= 32, "Linux machine ID should be at least 32 characters");
    
    // Should not contain newlines or extra whitespace
    assert!(!machine_id.contains('\n'), "Machine ID should not contain newlines");
    assert!(!machine_id.starts_with(' '), "Machine ID should not start with whitespace");
    assert!(!machine_id.ends_with(' '), "Machine ID should not end with whitespace");
}

#[cfg(target_os = "macos")]
#[test]
fn test_macos_machine_id_format() {
    let machine_id = id().expect("Failed to get machine ID on macOS");
    
    // macOS IOPlatformUUID is typically in UUID format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    assert!(machine_id.len() >= 36, "macOS machine ID should be at least 36 characters (UUID format)");
    
    // Should not contain newlines or extra whitespace
    assert!(!machine_id.contains('\n'), "Machine ID should not contain newlines");
    assert!(!machine_id.starts_with(' '), "Machine ID should not start with whitespace");
    assert!(!machine_id.ends_with(' '), "Machine ID should not end with whitespace");
}

#[cfg(target_os = "windows")]
#[test]
fn test_windows_machine_id_format() {
    let machine_id = id().expect("Failed to get machine ID on Windows");
    
    // Windows MachineGuid is typically in GUID format: XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
    assert!(machine_id.len() >= 36, "Windows machine ID should be at least 36 characters (GUID format)");
    
    // Should not contain newlines or extra whitespace
    assert!(!machine_id.contains('\n'), "Machine ID should not contain newlines");
    assert!(!machine_id.starts_with(' '), "Machine ID should not start with whitespace");
    assert!(!machine_id.ends_with(' '), "Machine ID should not end with whitespace");
}
