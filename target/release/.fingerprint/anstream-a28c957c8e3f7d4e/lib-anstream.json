{"rustc": 12610991425282158916, "features": "[\"auto\", \"default\", \"wincon\"]", "declared_features": "[\"auto\", \"default\", \"test\", \"wincon\"]", "target": 11278316191512382530, "profile": 8954424932545832044, "path": 9460974202620678123, "deps": [[384403243491392785, "colorchoice", false, 7577890915188830351], [6062327512194961595, "is_terminal_polyfill", false, 12529942364989261419], [9394696648929125047, "anstyle", false, 16326833396047164477], [11410867133969439143, "anstyle_parse", false, 11253445912339213541], [12500913394773746471, "anstyle_query", false, 11287360307273583967], [17716308468579268865, "utf8parse", false, 13428619941720972162]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/anstream-a28c957c8e3f7d4e/dep-lib-anstream", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}