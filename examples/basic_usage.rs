//! Basic usage example for get_machineid crate
//!
//! This example demonstrates how to get the raw machine ID from the system.

use get_machineid::id;

fn main() {
    match id() {
        Ok(machine_id) => {
            println!("Machine ID: {}", machine_id);
            println!("Length: {} characters", machine_id.len());
        }
        Err(e) => {
            eprintln!("Error getting machine ID: {}", e);
            std::process::exit(1);
        }
    }
}
