{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17001665395952474378, "build_script_build", false, 1160556806658154300]], "local": [{"RerunIfChanged": {"output": "release/build/typenum-b691a9b5d5bfa47f/output", "paths": ["tests"]}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 0, "compile_kind": 0}