{"rustc": 12610991425282158916, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 12613628788268674035, "path": 3833266431416864318, "deps": [[3060637413840920116, "proc_macro2", false, 5938935229155269043], [4974441333307933176, "syn", false, 12763518410846154047], [13077543566650298139, "heck", false, 636425081988493702], [17990358020177143287, "quote", false, 7587383367835767372]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_derive-b9f6d157986fd526/dep-lib-clap_derive", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}