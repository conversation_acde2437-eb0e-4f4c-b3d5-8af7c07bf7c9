{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"reset\", \"std\"]", "target": 12991177224612424488, "profile": 8276155916380437441, "path": 16303189959414909244, "deps": [[17475753849556516473, "digest", false, 16888758980106883682]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hmac-4b67b3f1fc9ece24/dep-lib-hmac", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}