# get_machineid

[![Crates.io](https://img.shields.io/crates/v/get_machineid.svg)](https://crates.io/crates/get_machineid)
[![Documentation](https://docs.rs/get_machineid/badge.svg)](https://docs.rs/get_machineid)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

Cross-platform Rust library for reading unique machine IDs without admin privileges.

This crate provides support for reading the unique machine ID of most host operating systems without requiring administrator privileges. It's a Rust port of the popular Go library [machineid](https://github.com/denisbrodbeck/machineid).

## Features

- **Cross-platform**: Supports Linux, macOS, Windows, and BSD systems
- **No admin privileges required**: Works with standard user permissions
- **Hardware independent**: Doesn't rely on MAC addresses, BIOS, or CPU information
- **Cryptographically secure**: Provides HMAC-SHA256 hashed IDs for secure usage
- **Stable IDs**: Machine IDs remain consistent across reboots and system updates

## Installation

Add this to your `Cargo.toml`:

```toml
[dependencies]
get_machineid = "0.1.0"
```

## Usage

### Basic Usage

```rust
use get_machineid::id;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let machine_id = id()?;
    println!("Machine ID: {}", machine_id);
    Ok(())
}
```

### Secure Usage (Recommended)

For production applications, use the `protected_id` function which returns a cryptographically secure hash:

```rust
use get_machineid::protected_id;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_id = "my_awesome_app";
    let protected_machine_id = protected_id(app_id)?;
    println!("Protected ID: {}", protected_machine_id);
    Ok(())
}
```

## API Reference

### `id() -> Result<String, MachineIdError>`

Returns the platform-specific machine ID of the current host OS.

**⚠️ Security Warning**: The returned ID should be considered "confidential" and must not be exposed in untrusted environments. Consider using `protected_id()` instead.

### `protected_id(app_id: &str) -> Result<String, MachineIdError>`

Returns a cryptographically secure hash of the machine ID using HMAC-SHA256, keyed by the machine ID and your application identifier.

- `app_id`: A unique identifier for your application
- Returns: A 64-character hex-encoded string

## Platform Support

The library uses different methods to obtain machine IDs on different platforms:

| Platform | Method | Source |
|----------|--------|--------|
| **Linux** | Read from filesystem | `/var/lib/dbus/machine-id` or `/etc/machine-id` |
| **macOS** | Execute system command | `ioreg -rd1 -c IOPlatformExpertDevice` (IOPlatformUUID) |
| **Windows** | Read from registry | `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography\MachineGuid` |
| **BSD** | Read from filesystem + fallback | `/etc/hostid` or `kenv -q smbios.system.uuid` |

## Security Considerations

A machine ID uniquely identifies the host system. Therefore:

1. **Never expose raw machine IDs** in logs, APIs, or user interfaces
2. **Always use `protected_id()`** for application-specific identification
3. **Use unique app IDs** to ensure different applications get different protected IDs
4. **Consider the ID confidential** - treat it like a secret key

The `protected_id` function uses HMAC-SHA256 to create a secure, application-specific identifier that cannot be reversed to obtain the original machine ID.

## Examples

Run the included examples:

```bash
# Basic usage
cargo run --example basic_usage

# Protected ID usage
cargo run --example protected_id
```

## Testing

Run the test suite:

```bash
# Run all tests
cargo test

# Run tests with output
cargo test -- --nocapture

# Run integration tests only
cargo test --test integration_tests
```

## Reliability Notes

- **Machine IDs are generally stable** for the OS installation and usually persist through updates or hardware changes
- **Cloned/imaged systems** may have identical machine IDs (this is expected behavior)
- **Virtual machines** may have predictable or duplicate machine IDs depending on the virtualization platform

### Generating New IDs

If you need to generate new machine IDs:

- **Linux**: Use `dbus-uuidgen` and update `/var/lib/dbus/machine-id` and `/etc/machine-id`
- **Windows**: Use the `sysprep` toolchain for proper system preparation
- **macOS/BSD**: Consult system documentation for platform-specific methods

## Error Handling

The library returns `MachineIdError` for various failure conditions:

```rust
use get_machineid::{id, MachineIdError};

match id() {
    Ok(machine_id) => println!("Success: {}", machine_id),
    Err(MachineIdError { .. }) => eprintln!("Failed to get machine ID"),
}
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

This crate is a Rust port of the excellent Go library [machineid](https://github.com/denisbrodbeck/machineid) by Denis Brodbeck.
