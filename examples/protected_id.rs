//! Protected ID example for get_machineid crate
//!
//! This example demonstrates how to get a cryptographically secure hash
//! of the machine ID using HMAC-SHA256.

use get_machineid::{id, protected_id};

fn main() {
    // Get the raw machine ID (consider this confidential!)
    let raw_id = match id() {
        Ok(id) => id,
        Err(e) => {
            eprintln!("Error getting machine ID: {}", e);
            std::process::exit(1);
        }
    };

    // Get a protected version for your application
    let app_name = "MyAwesomeApp";
    let protected = match protected_id(app_name) {
        Ok(id) => id,
        Err(e) => {
            eprintln!("Error getting protected ID: {}", e);
            std::process::exit(1);
        }
    };

    println!("Raw Machine ID: {}", raw_id);
    println!("Protected ID for '{}': {}", app_name, protected);
    println!();
    println!("Note: The raw machine ID should be considered confidential.");
    println!("Use the protected ID for application-specific identification.");
    
    // Demonstrate that different app names produce different protected IDs
    let other_app = "AnotherApp";
    let other_protected = protected_id(other_app).unwrap();
    println!();
    println!("Protected ID for '{}': {}", other_app, other_protected);
    println!("Different app names produce different protected IDs!");
}
