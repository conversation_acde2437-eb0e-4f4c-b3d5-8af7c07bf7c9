{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"reset\", \"std\"]", "target": 12991177224612424488, "profile": 2040997289075261528, "path": 16303189959414909244, "deps": [[17475753849556516473, "digest", false, 8758851945695548972]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hmac-074b335340d12f28/dep-lib-hmac", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}