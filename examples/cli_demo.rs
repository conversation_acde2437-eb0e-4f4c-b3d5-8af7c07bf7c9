//! CLI demonstration example
//!
//! This example shows how to use the get_machineid CLI programmatically
//! by calling the binary and parsing its output.

use std::process::Command;

fn main() {
    println!("=== get_machineid CLI Demonstration ===\n");

    // Test basic usage (raw machine ID)
    println!("1. Getting raw machine ID:");
    match run_cli(&[]) {
        Ok(output) => println!("   Raw ID: {}", output.trim()),
        Err(e) => println!("   Error: {}", e),
    }

    // Test explicit raw flag
    println!("\n2. Getting raw machine ID (explicit):");
    match run_cli(&["--raw"]) {
        Ok(output) => println!("   Raw ID: {}", output.trim()),
        Err(e) => println!("   Error: {}", e),
    }

    // Test protected ID
    println!("\n3. Getting protected machine ID:");
    match run_cli(&["--protected", "DemoApp"]) {
        Ok(output) => println!("   Protected ID: {}", output.trim()),
        Err(e) => println!("   Error: {}", e),
    }

    // Test legacy syntax
    println!("\n4. Getting protected machine ID (legacy syntax):");
    match run_cli(&["--appid", "DemoApp"]) {
        Ok(output) => println!("   Protected ID: {}", output.trim()),
        Err(e) => println!("   Error: {}", e),
    }

    // Test version
    println!("\n5. Getting version:");
    match run_cli(&["--version"]) {
        Ok(output) => println!("   Version: {}", output.trim()),
        Err(e) => println!("   Error: {}", e),
    }

    // Test error handling
    println!("\n6. Testing error handling (empty app ID):");
    match run_cli(&["--protected", ""]) {
        Ok(output) => println!("   Unexpected success: {}", output.trim()),
        Err(e) => println!("   Expected error: {}", e),
    }

    println!("\n=== CLI Demo Complete ===");
}

fn run_cli(args: &[&str]) -> Result<String, String> {
    let output = Command::new("cargo")
        .arg("run")
        .arg("--")
        .args(args)
        .output()
        .map_err(|e| format!("Failed to execute command: {}", e))?;

    if output.status.success() {
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    } else {
        Err(String::from_utf8_lossy(&output.stderr).to_string())
    }
}
