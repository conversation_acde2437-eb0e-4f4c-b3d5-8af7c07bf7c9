{"rustc": 12610991425282158916, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9589772125425470163, "path": 4508661205163583630, "deps": [[5820056977320921005, "anstream", false, 5186733985992536252], [9394696648929125047, "anstyle", false, 12638131428922994382], [11166530783118767604, "strsim", false, 13023969199359462470], [11649982696571033535, "clap_lex", false, 16736886007616688646]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-fa28f1b5739e9588/dep-lib-clap_builder", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}