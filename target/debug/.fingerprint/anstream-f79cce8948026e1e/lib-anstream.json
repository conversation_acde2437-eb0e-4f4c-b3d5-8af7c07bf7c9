{"rustc": 12610991425282158916, "features": "[\"auto\", \"default\", \"wincon\"]", "declared_features": "[\"auto\", \"default\", \"test\", \"wincon\"]", "target": 11278316191512382530, "profile": 7319384940312281862, "path": 9460974202620678123, "deps": [[384403243491392785, "colorchoice", false, 629526866570989065], [6062327512194961595, "is_terminal_polyfill", false, 11341224215794226554], [9394696648929125047, "anstyle", false, 6935017148800576400], [11410867133969439143, "anstyle_parse", false, 17229417583090049264], [12500913394773746471, "anstyle_query", false, 18395569477876369079], [17716308468579268865, "utf8parse", false, 2950909215230510867]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anstream-f79cce8948026e1e/dep-lib-anstream", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}