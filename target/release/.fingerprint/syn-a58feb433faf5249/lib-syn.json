{"rustc": 12610991425282158916, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 17710424885886743353, "deps": [[1988483478007900009, "unicode_ident", false, 13285273116223126274], [3060637413840920116, "proc_macro2", false, 5938935229155269043], [17990358020177143287, "quote", false, 7587383367835767372]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-a58feb433faf5249/dep-lib-syn", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}