{"rustc": 12610991425282158916, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 5129752230091461406, "path": 4508661205163583630, "deps": [[5820056977320921005, "anstream", false, 5964895599320647332], [9394696648929125047, "anstyle", false, 6935017148800576400], [11166530783118767604, "strsim", false, 14050259614135118800], [11649982696571033535, "clap_lex", false, 8910491719931595626]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-7bf1805d2a5df73e/dep-lib-clap_builder", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}