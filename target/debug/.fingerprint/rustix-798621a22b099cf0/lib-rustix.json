{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2734000389832276527, "path": 10262289570693138755, "deps": [[4684437522915235464, "libc", false, 13068427882726405407], [7896293946984509699, "bitflags", false, 1265833619285141235], [8253628577145923712, "libc_errno", false, 8117575018571823225], [10004434995811528692, "build_script_build", false, 3681409871201006297]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-798621a22b099cf0/dep-lib-rustix", "checksum": false}}], "rustflags": ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup", "-C", "target-feature=+crt-static"], "config": 2069994364910194474, "compile_kind": 0}